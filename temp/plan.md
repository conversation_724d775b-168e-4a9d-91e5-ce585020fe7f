# Development Plan: MessageServiceImpl.kt TODOs

## Overview

This plan addresses the TODOs found in `MessageServiceImpl.kt` related to improving the message insertion process by consolidating child-parent relationship management within the DAO layer instead of handling it separately in the service layer.

## Current Issues

### TODO 1: Line 275 - `saveUserMessage` method
```kotlin
// TODO: update MessageDao.insertUserMessage to handle this
if (parentMessageId != null) {
    messageDao.addChildToMessage(parentMessageId, userMessage.id).getOrElse { linkError ->
        // Error handling...
    }
}
```

### TODO 2: Line 338 - `saveAssistantMessage` method
```kotlin
// TODO: update MessageDao.insertAssistantMessage to handle this
messageDao.addChildToMessage(userMessage.id, assistantMsg.id).mapLeft { linkError ->
    // Error handling...
}
```

## Problem Analysis

Currently, the service layer performs message insertion in multiple steps:
1. Insert the message via `insertUserMessage`/`insertAssistantMessage`
2. Separately call `addChildToMessage` to establish parent-child relationships
3. Handle errors from both operations independently

This approach has several issues:
- **Transaction consistency**: Multiple DAO calls that should be atomic
- **Error handling complexity**: Two separate error paths for what should be one operation
- **Performance**: Multiple database round trips
- **Data consistency**: Risk of orphaned messages if the second operation fails

## Proposed Solution

### Phase 1: Extend Error Types

#### 1.1 Extend `InsertMessageError`
Add new error cases to handle child relationship errors during insertion:

```kotlin
sealed interface InsertMessageError {
    // Existing errors...
    data class SessionNotFound(val sessionId: Long) : InsertMessageError
    data class ParentNotInSession(val parentId: Long, val sessionId: Long) : InsertMessageError
    
    // New errors for child relationship handling
    data class ParentNotFound(val parentId: Long) : InsertMessageError
    data class ChildAlreadyExists(val parentId: Long, val childId: Long) : InsertMessageError
    data class ChildIsParent(val parentId: Long, val childId: Long) : InsertMessageError
}
```

### Phase 2: Update DAO Interface

#### 2.1 Update `MessageDao` interface
Modify the insertion methods to handle child relationships atomically:

```kotlin
interface MessageDao {
    /**
     * Inserts a new user message and automatically handles parent-child relationships.
     * If parentMessageId is provided, adds this message as a child to the parent.
     * @param sessionId The ID of the session the message belongs to.
     * @param content The text content of the message.
     * @param parentMessageId Optional ID of the parent message (null for root messages).
     * @return Either a [InsertMessageError] or the newly created [ChatMessage.UserMessage].
     */
    suspend fun insertUserMessage(
        sessionId: Long,
        content: String,
        parentMessageId: Long?
    ): Either<InsertMessageError, ChatMessage.UserMessage>

    /**
     * Inserts a new assistant message and automatically handles parent-child relationships.
     * If parentMessageId is provided, adds this message as a child to the parent.
     * @param sessionId The ID of the session the message belongs to.
     * @param content The text content of the message.
     * @param parentMessageId Optional ID of the parent message (null for root messages).
     * @param modelId Optional ID of the model used (for assistant messages).
     * @param settingsId Optional ID of the settings profile used (for assistant messages).
     * @return Either a [InsertMessageError] or the newly created [ChatMessage.AssistantMessage].
     */
    suspend fun insertAssistantMessage(
        sessionId: Long,
        content: String,
        parentMessageId: Long?,
        modelId: Long?,
        settingsId: Long?
    ): Either<InsertMessageError, ChatMessage.AssistantMessage>
    
    // Keep existing addChildToMessage for other use cases
    suspend fun addChildToMessage(parentId: Long, childId: Long): Either<MessageAddChildError, Unit>
}
```

### Phase 3: Update DAO Implementation

#### 3.1 Modify `MessageDaoExposed`
Update the implementation to handle child relationships within the same transaction:

**For `insertUserMessage`:**
1. Validate session exists
2. If `parentMessageId` provided:
   - Validate parent exists and belongs to the same session
   - Validate parent is not the same as the new message (prevent after insertion)
3. Insert the message
4. If `parentMessageId` provided:
   - Update parent's `childrenMessageIds` list to include the new message
5. Return the new message with proper error handling

**For `insertAssistantMessage`:**
1. Follow same pattern as user message
2. Additionally insert into `AssistantMessageTable`
3. Handle both message and assistant-specific data atomically

#### 3.2 Error Mapping
Map `MessageAddChildError` cases to appropriate `InsertMessageError` cases:
- `MessageAddChildError.ParentNotFound` → `InsertMessageError.ParentNotFound`
- `MessageAddChildError.ChildAlreadyExists` → `InsertMessageError.ChildAlreadyExists`
- `MessageAddChildError.ChildIsParent` → `InsertMessageError.ChildIsParent`
- `MessageAddChildError.ChildNotInSession` → `InsertMessageError.ParentNotInSession`

### Phase 4: Update Service Layer

#### 4.1 Simplify `MessageServiceImpl`
Remove the separate `addChildToMessage` calls and update error handling:

**In `saveUserMessage`:**
```kotlin
private suspend fun saveUserMessage(
    sessionId: Long,
    content: String,
    parentMessageId: Long?
): ChatMessage.UserMessage = transactionScope.transaction {
    // 1. Insert user message (now handles child relationships internally)
    val userMessage = messageDao.insertUserMessage(sessionId, content, parentMessageId).getOrElse { daoError ->
        throw IllegalStateException(
            "Failed to insert user message. " +
                    "Session id: $sessionId. Parent message id: $parentMessageId. Error: $daoError"
        )
    }

    // 2. Update session's leaf message ID
    sessionDao.updateSessionLeafMessageId(sessionId, userMessage.id).getOrElse { updateError ->
        throw IllegalStateException(
            "Failed to update session leaf message ID. " +
                    "Session id: $sessionId. New leaf message id: ${userMessage.id}. Error: $updateError"
        )
    }

    // 3. Return user message
    userMessage
}
```

**In `saveAssistantMessage`:**
```kotlin
private suspend fun saveAssistantMessage(
    sessionId: Long,
    content: String,
    userMessage: ChatMessage.UserMessage,
    model: LLMModel,
    settings: ModelSettings
): Pair<ChatMessage.AssistantMessage, ChatMessage.UserMessage> =
    transactionScope.transaction {
        // 1. Insert assistant message (now handles child relationships internally)
        val assistantMsg = messageDao.insertAssistantMessage(
            sessionId, content, userMessage.id, model.id, settings.id
        ).getOrElse { daoError ->
            throw IllegalStateException(
                "Failed to insert assistant message. " +
                        "Session id: $sessionId. Parent message id: ${userMessage.id}. Error: $daoError"
            )
        }

        // 2. Update session's leaf message ID
        sessionDao.updateSessionLeafMessageId(sessionId, assistantMsg.id).getOrElse { updateError ->
            throw IllegalStateException(
                "Failed to update session leaf message ID. " +
                        "Session id: $sessionId. New leaf message id: ${assistantMsg.id}. Error: $updateError"
            )
        }

        // 3. Retrieve updated user message
        val updatedUserMsg = messageDao.getMessageById(userMessage.id).getOrElse { daoError ->
            throw IllegalStateException(
                "Failed to retrieve updated user message. " +
                        "User message id: ${userMessage.id}. Error: $daoError"
            )
        } as ChatMessage.UserMessage

        assistantMsg to updatedUserMsg
    }
```

### Phase 5: Update Documentation

#### 5.1 Update KDocs in `MessageDao.kt`
Update the method documentation to reflect the new behavior:
- Clarify that parent-child relationships are handled automatically
- Update parameter descriptions
- Update return type documentation to include new error cases

#### 5.2 Update Implementation Documentation
Update comments in `MessageDaoExposed.kt` to reflect the new implementation approach.

## Implementation Order

1. **Phase 1**: Extend `InsertMessageError` with new error cases
2. **Phase 2**: Update `MessageDao` interface documentation
3. **Phase 3**: Implement changes in `MessageDaoExposed`
4. **Phase 4**: Update `MessageServiceImpl` to use the new behavior
5. **Phase 5**: Update all documentation and KDocs

## Testing Strategy

### Unit Tests
- Test new error cases in `InsertMessageError`
- Test atomic behavior of insert operations
- Test rollback behavior when child relationship fails
- Test existing functionality remains unchanged

### Integration Tests
- Test complete message flow with parent-child relationships
- Test error scenarios with invalid parent IDs
- Test session consistency

## Benefits

1. **Atomicity**: Child relationship management happens in the same transaction as message insertion
2. **Simplified Service Layer**: Removes duplicate error handling and multiple DAO calls
3. **Better Performance**: Single database transaction instead of multiple operations
4. **Improved Error Handling**: Single error path for the complete operation
5. **Data Consistency**: Eliminates risk of orphaned messages or inconsistent relationships

## Risks and Mitigation

### Risk 1: Breaking Changes
- **Mitigation**: Keep existing `addChildToMessage` method for other use cases
- **Mitigation**: Thorough testing to ensure existing functionality works

### Risk 2: Transaction Complexity
- **Mitigation**: Comprehensive error handling and rollback testing
- **Mitigation**: Clear transaction boundaries in implementation

### Risk 3: Error Mapping Complexity
- **Mitigation**: Clear mapping between `MessageAddChildError` and `InsertMessageError`
- **Mitigation**: Comprehensive unit tests for error scenarios

## Future Considerations

- Consider deprecating standalone `addChildToMessage` if no other use cases exist
- Consider adding batch insert operations for multiple messages
- Consider optimizing child list updates for large conversation trees
