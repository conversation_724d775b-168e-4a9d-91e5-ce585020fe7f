# Development Plan: Session Leaf Message Update on Message Deletion

## Overview
This plan addresses the TODO in `MessageService.kt` for updating the session's `currentLeafMessageId` when a message is deleted. The current implementation deletes messages recursively but doesn't handle the session's leaf message pointer, which can leave the session pointing to a non-existent message.

## Background Context

### Current Message Threading Structure
- Messages form a tree structure with `parentMessageId` and `childrenMessageIds`
- Each session has a `currentLeafMessageId` that points to the "active" leaf message
- This determines which conversation branch is displayed in the UI
- Messages are deleted recursively (message + all descendants)

### Current Delete Behavior
The existing `deleteMessage` implementation in `MessageDaoExposed`:
1. Recursively deletes the target message and all its children
2. Updates the parent's `childrenMessageIds` list to remove the deleted message
3. **Missing**: Updates the session's `currentLeafMessageId` if affected

## Problem Analysis

### Scenarios to Handle

1. **Deleting a non-leaf message**: The leaf message is a descendant of the deleted message
   - Session's `currentLeafMessageId` becomes invalid (points to deleted message)
   - Need to find a new appropriate leaf message

2. **Deleting a leaf message that is the current leaf**: Direct impact
   - Session's `currentLeafMessageId` becomes invalid
   - Need to traverse up to parent and find alternative path or set to parent

3. **Deleting a message in a different branch**: No impact
   - Session's `currentLeafMessageId` remains valid
   - No action needed

## Technical Requirements

### New Methods Needed

1. **In MessageService**:
   ```kotlin
   private suspend fun updateSessionLeafMessageAfterDeletion(
       sessionId: Long, 
       deletedMessageId: Long,
       deletedMessageParentId: Long?
   ): Either<DeleteMessageError, Unit>
   ```

2. **Helper methods**:
   ```kotlin
   private fun findLeafInSubtree(
       rootMessageId: Long,
       messageMap: Map<Long, ChatMessage>
   ): Long
   
   private fun findFirstAvailableRootMessage(
       sessionId: Long, 
       deletedMessageId: Long,
       messageMap: Map<Long, ChatMessage>
   ): Long?
   
   private fun isMessageInPath(
       targetMessageId: Long, 
       leafMessageId: Long,
       messageMap: Map<Long, ChatMessage>
   ): Boolean
   ```

### Algorithm Logic

#### Step 1: Determine if update is needed
```kotlin
// Check if the deleted message affects the current leaf
val currentSession = sessionDao.getSessionById(sessionId).bind()
val currentLeafId = currentSession.currentLeafMessageId

if (currentLeafId == null) {
    // No current leaf, no update needed
    return Unit.right()
}

// Check if deleted message is in the path from root to current leaf
val needsUpdate = isMessageInPath(deletedMessageId, currentLeafId)
```

#### Step 2: Find new leaf message
```kotlin
val newLeafMessageId = if (deletedMessage.parentMessageId == null) {
    // Deleting a root message - find first available root message
    findFirstRootMessage(sessionId)
} else {
    // Check if parent has other children after deletion
    val parent = messageDao.getMessageById(deletedMessage.parentMessageId).bind()
    val remainingChildren = parent.childrenMessageIds.filter { it != deletedMessageId }
    
    if (remainingChildren.isEmpty()) {
        // Parent becomes the new leaf
        deletedMessage.parentMessageId
    } else {
        // Traverse first remaining child to find its leaf
        findLeafInSubtree(remainingChildren.first())
    }
}
```

#### Step 3: Update session
```kotlin
sessionDao.updateSessionLeafMessageId(sessionId, newLeafMessageId).bind()
```

## Implementation Plan

### Phase 1: Core Algorithm Implementation
**Files to modify**: `MessageServiceImpl.kt`

1. **Add efficient message mapping and leaf detection logic**:
   ```kotlin
   private suspend fun isMessageInPath(
       targetMessageId: Long, 
       leafMessageId: Long, 
       messageMap: Map<Long, ChatMessage>
   ): Boolean {
       var currentId: Long? = leafMessageId
       while (currentId != null) {
           if (currentId == targetMessageId) return true
           currentId = messageMap[currentId]?.parentMessageId
       }
       return false
   }
   ```

2. **Add optimized leaf finding utilities using message map**:
   ```kotlin
   private fun findLeafInSubtree(rootMessageId: Long, messageMap: Map<Long, ChatMessage>): Long {
       var currentId = rootMessageId
       while (true) {
           val message = messageMap[currentId] 
               ?: throw IllegalStateException("Message $currentId not found in message map")
           if (message.childrenMessageIds.isEmpty()) {
               return currentId
           }
           // Follow first child path
           currentId = message.childrenMessageIds.first()
       }
   }
   
   private fun findFirstAvailableRootMessage(
       sessionId: Long, 
       deletedMessageId: Long,
       messageMap: Map<Long, ChatMessage>
   ): Long? {
       // Find all root messages (parentMessageId == null) excluding the deleted one
       return messageMap.values
           .filter { it.sessionId == sessionId && it.parentMessageId == null && it.id != deletedMessageId }
           .minByOrNull { it.createdAt }  // Use oldest root as the new active branch
           ?.id
   }
   ```

3. **Add main update logic with efficient message mapping**:
   ```kotlin
   private suspend fun updateSessionLeafMessageAfterDeletion(
       sessionId: Long,
       deletedMessageId: Long,
       deletedMessageParentId: Long?
   ): Either<DeleteMessageError, Unit> = either {
       val session = sessionDao.getSessionById(sessionId).bind()
       val currentLeafId = session.currentLeafMessageId ?: return Unit.right()
       
       // Create efficient message map for the entire session
       val allMessages = messageDao.getMessagesBySessionId(sessionId)
       val messageMap = allMessages.associateBy { it.id }
       
       // Check if the deleted message affects current leaf
       if (!isMessageInPath(deletedMessageId, currentLeafId, messageMap)) {
           return Unit.right() // No update needed
       }
       
       val newLeafId = when (deletedMessageParentId) {
           null -> {
               // Deleted a root message, find another available root
               val nextRootId = findFirstAvailableRootMessage(sessionId, deletedMessageId, messageMap)
               if (nextRootId != null) {
                   // Traverse down to find the leaf of this root
                   findLeafInSubtree(nextRootId, messageMap)
               } else {
                   // No more root messages, session becomes empty
                   null
               }
           }
           else -> {
               // Get parent's current state (before deletion updates are reflected in map)
               val parent = messageMap[deletedMessageParentId]
                   ?: throw IllegalStateException("Parent message $deletedMessageParentId not found")
               
               // Calculate remaining children after deletion
               val remainingChildren = parent.childrenMessageIds.filter { it != deletedMessageId }
               
               if (remainingChildren.isEmpty()) {
                   // Parent becomes the new leaf
                   deletedMessageParentId
               } else {
                   // Find leaf in first remaining child's subtree
                   findLeafInSubtree(remainingChildren.first(), messageMap)
               }
           }
       }
       
       sessionDao.updateSessionLeafMessageId(sessionId, newLeafId).bind()
   }
   ```

### Phase 2: Integration with Delete Method
**Files to modify**: `MessageServiceImpl.kt`

1. **Update deleteMessage method**:
   ```kotlin
   override suspend fun deleteMessage(id: Long): Either<DeleteMessageError, Unit> =
       transactionScope.transaction {
           either {
               // Get message details before deletion
               val messageToDelete = withError({ daoError: MessageError.MessageNotFound ->
                   DeleteMessageError.MessageNotFound(daoError.id)
               }) {
                   messageDao.getMessageById(id).bind()
               }
               
               val sessionId = messageToDelete.sessionId
               val parentId = messageToDelete.parentMessageId
               
               // Perform the deletion
               withError({ daoError: MessageError.MessageNotFound ->
                   DeleteMessageError.MessageNotFound(daoError.id)
               }) {
                   messageDao.deleteMessage(id).bind()
               }
               
               // Update session leaf message if needed
               updateSessionLeafMessageAfterDeletion(sessionId, id, parentId).bind()
           }
       }
   ```

### Phase 3: Error Handling Enhancement
**Files to modify**: `DeleteMessageError.kt`

1. **Add new error types independent of DAO layer**:
   ```kotlin
   package eu.torvian.chatbot.server.service.core.error.message

   /**
    * Represents possible errors when deleting a message.
    */
   sealed interface DeleteMessageError {
       /**
        * Indicates that the message with the specified ID was not found.
        */
       data class MessageNotFound(val id: Long) : DeleteMessageError

       /**
        * Indicates that updating the session's leaf message ID failed after successful message deletion.
        * This ensures the session state remains consistent even if the deletion succeeded.
        */
       data class SessionUpdateFailed(val sessionId: Long) : DeleteMessageError
   }
   ```

2. **Update main update logic to return service-level errors**:
   ```kotlin
   private suspend fun updateSessionLeafMessageAfterDeletion(
       sessionId: Long,
       deletedMessageId: Long,
       deletedMessageParentId: Long?
   ): Either<DeleteMessageError, Unit> = either {
       val session = withError({ _: SessionError ->
           DeleteMessageError.SessionUpdateFailed(sessionId)
       }) {
           sessionDao.getSessionById(sessionId).bind()
       }
       val currentLeafId = session.currentLeafMessageId ?: return Unit.right()
       
       // Create efficient message map for the entire session
       val allMessages = messageDao.getMessagesBySessionId(sessionId)
       val messageMap = allMessages.associateBy { it.id }
       
       // Check if the deleted message affects current leaf
       if (!isMessageInPath(deletedMessageId, currentLeafId, messageMap)) {
           return Unit.right() // No update needed
       }
       
       val newLeafId = when (deletedMessageParentId) {
           null -> {
               // Deleted a root message, find another available root
               val nextRootId = findFirstAvailableRootMessage(sessionId, deletedMessageId, messageMap)
               if (nextRootId != null) {
                   // Traverse down to find the leaf of this root
                   findLeafInSubtree(nextRootId, messageMap)
               } else {
                   // No more root messages, session becomes empty
                   null
               }
           }
           else -> {
               // Get parent's current state (before deletion updates are reflected in map)
               val parent = messageMap[deletedMessageParentId]
                   ?: throw IllegalStateException("Parent message $deletedMessageParentId not found")
               
               // Calculate remaining children after deletion
               val remainingChildren = parent.childrenMessageIds.filter { it != deletedMessageId }
               
               if (remainingChildren.isEmpty()) {
                   // Parent becomes the new leaf
                   deletedMessageParentId
               } else {
                   // Find leaf in first remaining child's subtree
                   findLeafInSubtree(remainingChildren.first(), messageMap)
               }
           }
       }
       
       withError({ _: SessionError ->
           DeleteMessageError.SessionUpdateFailed(sessionId)
       }) {
           sessionDao.updateSessionLeafMessageId(sessionId, newLeafId).bind()
       }
   }
   ```

3. **Update error mapping in MessageServiceImpl**:
   ```kotlin
   // In the deleteMessage method, the session update now returns DeleteMessageError directly
   updateSessionLeafMessageAfterDeletion(sessionId, id, parentId).bind()
   ```

4. **Remove DAO layer dependency**:
   - No import of `SessionError` needed in `DeleteMessageError.kt`
   - Service layer errors remain independent from DAO layer implementation details
   - Error mapping happens within the service implementation, not exposed in the interface

### Phase 4: Testing Strategy

#### Unit Tests
**Files to create/modify**: 
- `MessageServiceImplTest.kt`
- Add test cases for various deletion scenarios

**Test Cases**:
1. **Delete leaf message (current leaf)**: Verify session points to parent
2. **Delete middle message**: Verify session points to first child's leaf
3. **Delete root message**: Verify session points to another root or null
4. **Delete non-affecting message**: Verify session leaf unchanged
5. **Delete in empty session**: Verify graceful handling
6. **Delete last message**: Verify session leaf becomes null

#### Integration Tests
**Files to create/modify**:
- `MessageDeletionIntegrationTest.kt`

**Test Scenarios**:
1. Complex tree structures with multiple branches
2. Sequential deletions
3. Deletion with concurrent operations

### Phase 5: Documentation Updates

1. **Update MessageService.kt**: Remove TODO and add comprehensive documentation
2. **Update API documentation**: Document the leaf message update behavior
3. **Add architectural notes**: Document the leaf message management strategy

## Risk Analysis

### Potential Issues
1. **Race conditions**: Multiple concurrent deletions could conflict
   - **Mitigation**: Transaction scope ensures atomicity
   
2. **Performance impact**: Tree traversals on every deletion
   - **Mitigation**: Limit to affected sessions only, optimize queries
   
3. **Complex tree scenarios**: Edge cases in deeply nested structures
   - **Mitigation**: Comprehensive test coverage

### Rollback Strategy
1. Keep the TODO implementation as a feature flag
2. Add monitoring for session leaf consistency
3. Implement repair utilities for inconsistent states

## Timeline Estimate

- **Phase 1 (Core Algorithm)**: 2-3 days
- **Phase 2 (Integration)**: 1 day  
- **Phase 3 (Error Handling)**: 1 day
- **Phase 4 (Testing)**: 2-3 days
- **Phase 5 (Documentation)**: 1 day

**Total**: 7-9 days

## Success Criteria

1. Session's `currentLeafMessageId` is never left pointing to a deleted message
2. After deletion, session points to the most appropriate remaining leaf message
3. Performance impact is minimal (< 10ms additional processing per deletion)
4. All existing functionality remains unchanged
5. 100% test coverage for new logic paths

## Future Enhancements

1. **Optimized leaf finding**: Cache leaf paths for performance
2. **User preference**: Allow users to choose which branch to follow after deletion
3. **Batch deletion optimization**: Handle multiple deletions efficiently
4. **Audit trail**: Log leaf message changes for debugging
