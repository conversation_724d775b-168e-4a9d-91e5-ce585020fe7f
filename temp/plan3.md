# MessageServiceImpl.deleteMessage Refactor Plan

## Current Issues

The `deleteMessage` function in `MessageServiceImpl` suffers from deep nesting and complex control flow:

1. **Deep Nesting**: Multiple levels of `either`, `withError`, and conditional statements
2. **Mixed Concerns**: Business logic, error handling, and transaction management are intertwined
3. **Complex Conditional Logic**: Nested `if` statements with complex branching for leaf update calculations
4. **Large Function Size**: The function is doing too many things in one place
5. **Hard to Test**: Individual components are difficult to unit test due to tight coupling
6. **Poor Readability**: The logic flow is hard to follow due to deep indentation

## Refactor Strategy

### 1. Extract Helper Classes/Data Classes

Create data classes to represent intermediate states and calculations:

```kotlin
data class MessageDeletionContext(
    val messageToDelete: ChatMessage,
    val sessionId: Long,
    val parentId: Long?,
    val session: ChatSession,
    val currentLeafId: Long?,
    val messageMap: Map<Long, ChatMessage>
)

data class LeafUpdateCalculation(
    val needsUpdate: <PERSON><PERSON><PERSON>,
    val newLeafId: Long?
)
```

### 2. Extract Business Logic Methods

Break down the complex logic into focused private methods:

#### a) Message Context Preparation
```kotlin
private suspend fun prepareMessageDeletionContext(id: Long): Either<DeleteMessageError, MessageDeletionContext>
```
- Fetch message to delete
- Fetch session information
- Build message map if needed

#### b) Leaf Update Calculation
```kotlin
private fun calculateLeafUpdate(
    messageId: Long,
    context: MessageDeletionContext
): LeafUpdateCalculation
```
- Check if deletion affects current leaf
- Calculate new leaf ID if needed
- Handle both root and non-root message deletion scenarios

#### c) New Leaf Calculation for Root Deletion
```kotlin
private fun calculateNewLeafForRootDeletion(
    deletedMessageId: Long,
    sessionId: Long,
    messageMap: Map<Long, ChatMessage>
): Long?
```

#### d) New Leaf Calculation for Non-Root Deletion
```kotlin
private fun calculateNewLeafForNonRootDeletion(
    parentId: Long,
    deletedMessageId: Long,
    messageMap: Map<Long, ChatMessage>
): Long
```

#### e) Deletion Execution
```kotlin
private suspend fun executeMessageDeletion(
    messageId: Long,
    sessionId: Long,
    leafUpdate: LeafUpdateCalculation
): Either<DeleteMessageError, Unit>
```

### 3. Refactored Function Structure

The main `deleteMessage` function will become a high-level orchestrator:

```kotlin
override suspend fun deleteMessage(id: Long): Either<DeleteMessageError, Unit> =
    transactionScope.transaction {
        either {
            // 1. Prepare deletion context
            val context = prepareMessageDeletionContext(id).bind()
            
            // 2. Calculate leaf update requirements
            val leafUpdate = calculateLeafUpdate(id, context)
            
            // 3. Execute deletion and updates
            executeMessageDeletion(id, context.sessionId, leafUpdate).bind()
        }
    }
```

### 4. Error Handling Improvements

#### a) Centralized Error Mapping
Create a utility function for consistent error mapping:
```kotlin
private inline fun <T> mapMessageDaoError(
    block: () -> Either<MessageError, T>
): Either<DeleteMessageError, T>

private inline fun <T> mapSessionDaoError(
    sessionId: Long,
    block: () -> Either<SessionError, T>
): Either<DeleteMessageError, T>
```

#### b) Use Extension Functions
Create extension functions for common error transformations:
```kotlin
private fun Either<MessageError.MessageNotFound, ChatMessage>.mapToDeleteError(): Either<DeleteMessageError.MessageNotFound, ChatMessage>

private fun Either<SessionError, ChatSession>.mapToSessionUpdateError(sessionId: Long): Either<DeleteMessageError.SessionUpdateFailed, ChatSession>
```

### 5. Performance Optimizations

#### a) Lazy Message Map Building
Only build the message map when leaf update is actually needed:
```kotlin
private suspend fun buildMessageMapIfNeeded(
    sessionId: Long,
    needsMap: Boolean
): Map<Long, ChatMessage> = if (needsMap) {
    messageDao.getMessagesBySessionId(sessionId).associateBy { it.id }
} else {
    emptyMap()
}
```

#### b) Early Returns
Use early returns to avoid unnecessary processing when possible.

### 6. Testing Strategy

With the refactored structure, each component becomes testable:

1. **Context Preparation**: Test with various message states and error conditions
2. **Leaf Calculation**: Test with different tree structures and deletion scenarios
3. **Deletion Execution**: Test transaction behavior and error handling
4. **Helper Functions**: Unit test each helper function independently

### 7. Benefits of This Refactor

1. **Reduced Nesting**: Maximum 2-3 levels of nesting instead of 6-7
2. **Single Responsibility**: Each function has one clear purpose
3. **Improved Readability**: Logic flow is easier to follow
4. **Better Testability**: Individual components can be unit tested
5. **Easier Maintenance**: Changes to specific logic can be made in isolation
6. **Performance**: Lazy loading of message map when not needed
7. **Error Handling**: Centralized and consistent error mapping

### 8. Implementation Order

1. Create data classes for intermediate states
2. Extract context preparation logic
3. Extract leaf calculation logic
4. Extract deletion execution logic
5. Create error mapping utilities
6. Refactor main function to use extracted methods
7. Add comprehensive tests for each extracted method
8. Verify integration tests still pass

### 9. Backwards Compatibility

This refactor maintains the same public interface and behavior, ensuring no breaking changes for consumers of the `MessageService`.
