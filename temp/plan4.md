# Development Plan: Add single-message delete in MessageService and DAO

Goal: Implement a new function to delete a single message (non-recursive) at both service and DAO levels, ensuring parent/children/sibling links remain consistent and the session's current leaf is updated appropriately when affected.

Scope:
- Add MessageService.deleteSingleMessage(id) alongside existing recursive deleteMessage(id).
- Add MessageDao.deleteSingle(id) in addition to existing recursive deleteMessage(id).
- Implement Exposed-specific logic in MessageDaoExposed.deleteSingle(id) to maintain childrenMessageIds and parentMessageId references correctly for a single-node deletion.
- Update session currentLeafMessageId logic for single delete in MessageServiceImpl.
- Optionally expose an HTTP route to explicitly request single delete (to avoid breaking existing behavior).
- Add tests covering single deletion behavior and session leaf updates.

Assumptions from current codebase:
- Message tree representation:
  - ChatMessageTable has: id, sessionId, role, content, createdAt, updatedAt, parentMessageId, childrenMessageIds (JSON string with ordered list of child IDs).
  - insertUserMessage/insertAssistantMessage append the new child ID to parent.childrenMessageIds via linkChildToParent.
  - deleteMessage(id) currently deletes recursively and removes id from parent.childrenMessageIds.
- Session current leaf semantics (from existing recursive delete):
  - If deletion affects the current leaf path, find a suitable replacement: parent or next available root branch’s leaf.
  - Helpers exist in MessageServiceImpl: isMessageInPath, findLeafInSubtree, findFirstAvailableRootMessage, calculateLeafUpdateIfNeeded (used by recursive delete).

Key differences for single delete:
- We delete only the target message row.
- We must decide how to reparent or promote children.
- We must update the parent’s childrenMessageIds list to remove the deleted id and possibly insert child ids in place.
- We must update each child’s parentMessageId to the deleted node’s parent (promotion).
- If the deleted node is a leaf, leaf updates are straightforward. If not, leaf may or may not be affected, depending on whether the current leaf is in deleted node’s subtree.

Design decisions for link updates on single delete:
- Promotion strategy (adopt children to grandparent):
  - Let D be deleted node, P = parent(D), C = ordered list of children(D) = [c1, c2, ...].
  - If P != null:
    - Remove D from P.children list.
    - Insert C in P.children at the index where D previously was, preserving the order of C.
    - For each ci in C, set parentMessageId(ci) = P.id.
  - If P == null (D was a root):
    - For each ci in C, set parentMessageId(ci) = null.
    - Effectively, children become new roots. Their relative order (createdAt already set) remains, but they are separate root threads.
  - In all cases, delete D’s row (and any assistant metadata row if it exists) and do not alter descendants beyond updating their immediate parent IDs.
- This maintains tree validity, preserves sibling order, and avoids cascading deletes.

Session current leaf update logic for single delete:
- Reuse the same calculateLeafUpdateIfNeeded() helper when possible, but adapt logic for single-delete promotions:
  - If the current leaf is not in D’s subtree, no change.
  - If current leaf is D itself:
    - If P != null:
      - If C is empty: new leaf = P (since we’ve removed the leaf).
      - If C is not empty: new leaf = findLeafInSubtree(C.first(), messageMapAfterPromotionButBeforeDeletion) i.e., the first promoted child’s left-most leaf.
    - If P == null (root):
      - If C is empty: choose the oldest remaining root leaf (as existing logic does) or null if no messages remain.
      - If C is not empty: new leaf = findLeafInSubtree(C.first(), ...), since children become roots, the first child’s subtree keeps the continuation.
  - If current leaf is in D’s subtree but not D itself: after promotion, the path to leaf changes in the segment where D was; the leaf likely remains the same id, but its ancestor parentMessageId chain is shortened by one. So set new leaf = existing currentLeafId, unless the promotion reordering chooses a different branch for the leaf when D was the first child on the path and was removed.
- Implementation approach:
  - Compute leaf update before executing DB changes, using a messageMap built from DAO prior to deletion, similar to recursive delete.
  - Derive the expected new leaf id considering promotion rules.
  - After DAO single-delete completes, update session currentLeafMessageId if needed.

API and Code changes
1) MessageDao interface
- Add:
  - suspend fun deleteSingle(id: Long): Either<MessageError.MessageNotFound, Unit>
- KDoc: Deletes only one message. Reparents/promotes its children to the message’s parent, preserving order, and maintains parent and children links.

2) MessageDaoExposed implementation
- Add implementation for deleteSingle(id): inside a transaction.
  Steps:
  - Load the message row for id. If not found -> Left(MessageNotFound).
  - Load parent row if any; parse parent.childrenMessageIds.
  - Parse D.childrenMessageIds into list C.
  - For each childId in C:
    - Update ChatMessageTable.parentMessageId = parentId (nullable if root).
  - Update parent’s childrenMessageIds:
    - If P != null:
      - Remove id from parent.children list.
      - Insert the sequence C at the same index where id was.
      - Write parent.childrenMessageIds back as JSON.
    - If P == null: nothing to update in parent.
  - Delete from AssistantMessageTable where messageId = id (if exists), then delete from ChatMessageTable where id = id.
  - Optional: clear D’s own children list first isn’t necessary since row will be deleted.
  - Constraints/edge cases validation:
    - Ensure parent and child sessionId match.
    - Ensure children list from parent contains D; if not, log and attempt to maintain consistency by simply inserting C at start or end—prefer throwing to surface corruption (as current recursive delete throws on mismatch). Follow current code’s strictness and throw IllegalStateException similar to deleteMessage.

3) MessageService interface
- Add method signature and KDoc:
  - suspend fun deleteSingleMessage(id: Long): Either<DeleteMessageError, Unit>
  - Document that it deletes exactly one message and promotes its children to the parent. Also describes session leaf updates.

4) MessageServiceImpl implementation
- Implement deleteSingleMessage(id): pattern similar to deleteMessage(id):
  - Fetch messageToDelete via DAO, map not found to DeleteMessageError.MessageNotFound.
  - Fetch session; compute leaf update with a new helper calculateLeafUpdateIfNeededForSingleDelete(messageToDelete, session, currentLeafId).
    - Option A: Reuse calculateLeafUpdateIfNeeded() by simulating the post-state rules:
      - For single delete, the remaining children of parent after deletion are: parent.children replacing D with C. For leaf calculation, we need to treat removal differently when D had children.
      - Implement a dedicated function calculateLeafUpdateForSingleDelete to be explicit and safer.
  - Call messageDao.deleteSingle(id).
  - If leafUpdateResult.needsUpdate: sessionDao.updateSessionLeafMessageId(sessionId, newLeafId).
- Helper: calculateLeafUpdateForSingleDelete(messageToDelete, session, currentLeafId):
  - Build messageMap from DAO getMessagesBySessionId(session.id) before deletion.
  - If currentLeafId == null -> no update.
  - If not isMessageInPath(D.id, currentLeafId) -> no update.
  - else:
    - If currentLeafId == D.id:
      - If P == null:
        - If C.isEmpty() -> findFirstAvailableRootMessage(session.id, D.id, messageMap)
        - else -> findLeafInSubtree(C.first(), messageMap)
      - If P != null:
        - If C.isEmpty() -> P.id
        - else -> findLeafInSubtree(C.first(), messageMap)
    - If currentLeafId != D.id (leaf is in D’s subtree):
      - The leaf id remains the same since we’re only changing ancestors.
      - Return currentLeafId.
  - Return LeafUpdateCalculation(needsUpdate = true, newLeafId = computedValue).

5) Ktor routes
- Provide a way to call single delete via HTTP without breaking existing DELETE behavior:
  Options:
  - Add a new sub-resource: DELETE /api/v1/messages/{messageId}/single
  - Or add query parameter mode=single to the existing route.
- Prefer a new sub-resource path for clarity if resources classes exist; otherwise, extend handler to check a query param.
- Update route to call messageService.deleteSingleMessage when requested.

6) Tests
- DAO tests (MessageDaoExposedTest):
  - Setup a session with a small tree: P -> D -> [c1, c2], and sibling s.
  - deleteSingle(D):
    - Expect c1.parent = P, c2.parent = P, P.children == [original children with D replaced by c1, c2 in correct index], s unaffected.
    - Ensure D row is deleted and AssistantMessage row if D was assistant.
  - deleteSingle(root D with children): children become roots (parent null). Parent count unaffected.
  - deleteSingle(leaf): simply remove from parent.children.

- Service tests (MessageServiceImplDeleteTest or new tests):
  - Validate session leaf updates per cases:
    - Deleting current leaf when it’s a leaf with parent -> leaf becomes parent.
    - Deleting current leaf root with children -> leaf becomes first child’s deepest leaf.
    - Deleting a non-leaf ancestor of the current leaf -> leaf remains the same id.
    - Deleting a message outside the current leaf path -> no change.

- Route tests (MessageRoutesTest):
  - Invoke new endpoint and verify 204, and state effects.

7) Error handling and logging
- Follow existing patterns: map DAO MessageNotFound to service error DeleteMessageError.MessageNotFound, Session update failures to DeleteMessageError.SessionUpdateFailed.
- Use logger.debug/info where appropriate in DAO and Service for traceability.

8) Transactions
- Keep DAO deleteSingle fully transactional.
- Service method wraps steps in transactionScope.transaction like existing methods.

9) Backward compatibility
- Existing deleteMessage(id) remains recursive and unchanged.
- New capability is additive.

10) Implementation outline (pseudocode)

DAO deleteSingle(id):
```
transaction {
  row = ChatMessageTable.leftJoin(AssistantMessageTable).select { id == id }.singleOrNull()
  ensure row != null -> Left(MessageNotFound(id))
  parentId = row.parentMessageId
  childrenJson = row.childrenMessageIds
  children = Json.decodeFromString<List<Long>>(childrenJson)

  if (parentId != null) {
    parentRow = ChatMessageTable.select { id == parentId }.single()
    parentChildren = Json.decodeFromString<MutableList<Long>>(parentRow.childrenMessageIds)
    val idx = parentChildren.indexOf(id)
    ensure(idx >= 0) { IllegalStateException("Child not in parent list") }
    // replace D with its children
    parentChildren.removeAt(idx)
    parentChildren.addAll(idx, children)
    ChatMessageTable.update({ id == parentId }) { it[childrenMessageIds] = Json.encodeToString(parentChildren) }
  }

  // reparent all children
  for (childId in children) {
    ChatMessageTable.update({ id == childId }) { it[parentMessageId] = parentId }
  }

  // delete assistant extra row if exists, then delete message row
  AssistantMessageTable.deleteWhere { messageId eq id }
  val deleted = ChatMessageTable.deleteWhere { ChatMessageTable.id eq id }
  ensure(deleted != 0) { IllegalStateException("Failed to delete $id") }
}
```

Service deleteSingleMessage(id):
```
transaction {
  val msg = messageDao.getMessageById(id).mapLeft -> DeleteMessageError.MessageNotFound
  val session = sessionDao.getSessionById(msg.sessionId)
  val leafUpdate = calculateLeafUpdateForSingleDelete(msg, session, session.currentLeafMessageId)
  messageDao.deleteSingle(id)
  if (leafUpdate.needsUpdate) sessionDao.updateSessionLeafMessageId(session.id, leafUpdate.newLeafId)
}
```

calculateLeafUpdateForSingleDelete:
```
if (currentLeafId == null) return noUpdate
val all = messageDao.getMessagesBySessionId(session.id)
val map = all.associateBy { it.id }
if (!isMessageInPath(D.id, currentLeafId, map)) return noUpdate
val C = D.childrenMessageIds
val P = D.parentMessageId
return when {
  currentLeafId == D.id && P == null && C.isEmpty() -> newLeaf = findFirstAvailableRootMessage(session.id, D.id, map)
  currentLeafId == D.id && P == null && C.isNotEmpty() -> newLeaf = findLeafInSubtree(C.first(), map)
  currentLeafId == D.id && P != null && C.isEmpty() -> newLeaf = P
  currentLeafId == D.id && P != null && C.isNotEmpty() -> newLeaf = findLeafInSubtree(C.first(), map)
  else -> newLeaf = currentLeafId // leaf is deeper in D’s subtree, it remains valid
}
```

11) Documentation updates
- KDoc on new methods. Optional docs markdown if you maintain API docs.

12) Rollout steps
- Implement DAO and Service changes.
- Add/adjust routes.
- Add tests and run them individually if failures are noisy due to parallelism.
- Run: ./gradlew server:test and ./gradlew server:assemble.

Edge cases to validate explicitly:
- Deleting a user message that has an assistant reply (promotion of assistant).
- Deleting an assistant message with no children (simple leaf removal).
- Deleting a root with many children.
- Data integrity issues: if parent.childrenMessageIds does not contain D (log and throw to match current strictness).
- Children list possibly empty or large; order preserved when promoting.
- Ensure kotlinx.datetime is used consistently (no new java.time usage).
